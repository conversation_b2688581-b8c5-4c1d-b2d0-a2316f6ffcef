<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Actions\ApproveCustomers;
use App\Jobs\MessageJobCreator;
use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\OneTimePassword;
use App\Models\RegistrationRequest;
use App\Models\Sender;
use App\Models\Setting;
use App\Rules\PhoneNumberRule;
use App\Services\Jasmin\JasminClient;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Throwable;

class RegistrationRequestController
{
    /**
     * @api {post} /api/registration-request Registration Request
     *
     * @unauthenticated
     *
     * @bodyParam name required string
     * @bodyParam phone required string
     * @bodyParam email required string
     * @bodyParam city nullable string
     * @bodyParam company nullable string
     * @bodyParam service_type nullable string
     * @bodyParam have_smsapi nullable string [yes, no, developing]
     * @bodyParam target nullable string [OTP, Campaign, Notification]
     * @bodyParam system_type nullable string
     * @bodyParam note nullable string
     *
     * @response json
     *
     * @group Registration Request
     */
    public function register(Request $request): JsonResponse
    {
        /** @var array{'name': string, 'phone': string, 'email':string|null, 'city': string|null, 'company': string|null, 'category': string|null, 'have_smsapi': string|null, 'target': string|null, 'system_type': string|null, 'notes': string|null} $data */
        $data = Validator::make(
            [
                'name' => $request->name,
                'phone' => $request->phone,
                'email' => $request->email,
                'city' => $request->city,
                'company' => $request->company,
                'category' => $request->category,
                'have_smsapi' => $request->have_smsapi,
                'target' => $request->target,
                'system_type' => $request->system_type,
                'notes' => $request->notes,
            ],
            [
                'name' => 'required|string',
                'phone' => ['required', new PhoneNumberRule()],
                'email' => 'required|email|unique:registration_requests,email',
                'city' => 'nullable|string',
                'company' => 'nullable|string',
                'category' => 'nullable|string',
                'have_smsapi' => 'nullable|string',
                'target' => 'nullable|array',
                'system_type' => 'nullable|string',
                'notes' => 'nullable|string',
            ],
        )->validate();

        RegistrationRequest::create($data);

        $otp = $this->attemptOtpSend($data['phone']);

        return response()->json([
            'message' => 'The request has been registered successfully.',
            'request_id' => $otp?->id,
        ], 201);
    }

    /**
     * @api {post} /api/registration-request/verify Verify Registration Request
     *
     * @unauthenticated
     *
     * @bodyParam request_id required uuid
     * @bodyParam code required string
     *
     * @response json
     *
     * @group Registration Request
     */
    public function verifyOtp(Request $request): JsonResponse
    {
        /** @var array{'request_id': string, 'code': string} $data */
        $data = Validator::make(
            [
                'request_id' => $request->request_id,
                'code' => $request->code,
            ],
            [
                'request_id' => 'required|uuid',
                'code' => 'required|string',
            ],
        )->validate();

        $otp = OneTimePassword::with('message.messages')->find($data['request_id']);

        if (! $otp) {
            return response()->json([
                'message' => 'OTP not found',
            ], 404);
        }

        if ($otp->verified_at) {
            return response()->json([
                'message' => 'OTP already verified',
            ], 400);
        }

        if ($otp->isExpired()) {
            return response()->json([
                'message' => 'OTP has expired',
            ], 401);
        }

        if ($otp->code !== $data['code']) {
            return response()->json([
                'message' => 'Invalid OTP',
            ], 401);
        }

        $message = $otp->message;

        if (! $message || $message->messages->isEmpty()) {
            return response()->json([
                'message' => 'Message not found',
            ], 404);
        }

        $phoneNumber = $message->messages->first()->number;

        $registrationRequest = RegistrationRequest::where('phone', $phoneNumber)->first();

        if (! $registrationRequest) {
            return response()->json([
                'message' => 'Registration request not found',
            ], 404);
        }

        try {
            DB::transaction(function () use ($registrationRequest, $otp): void {
                new ApproveCustomers()->execute($registrationRequest);

                // Mark OTP as verified
                $otp->verified_at = now();
                $otp->save();
            });
        } catch (Throwable $e) {
            Log::error('Registration Request Update Failed: '.$e->getMessage());

            return response()->json([
                'message' => 'Something went wrong',
            ], 500);
        }

        return response()->json([
            'message' => 'OTP verified successfully',

        ]);
    }

    /**
     * @api {post} /api/registration-request/resend Resend OTP
     *
     * @unauthenticated
     *
     * @bodyParam phone required string
     *
     * @response json
     *
     * @group Registration Request
     */
    public function resendOtp(Request $request): JsonResponse
    {
        /** @var array{'phone': string} $data */
        $data = Validator::make(
            [
                'phone' => $request->phone,
            ],
            [
                'phone' => ['required', new PhoneNumberRule()],
            ],
        )->validate();

        // check if there is no active otp for the phone number
        if ($this->checkPhoneNumberIfHaveActiveOTP($data['phone'])) {
            return response()->json([
                'message' => 'You have already sent an OTP to this number',
            ], 400);
        }

        $otp = $this->attemptOtpSend($data['phone']);

        return response()->json([
            'message' => 'OTP resent successfully',
            'request_id' => $otp?->id,
        ]);
    }

    private function checkPhoneNumberIfHaveActiveOTP(string $phoneNumber): bool
    {
        $lastMessage = MessageReceipt::where('number', $phoneNumber)
            ->latest('id')
            ->first();

        if (! $lastMessage) {
            return false;
        }

        $otp = OneTimePassword::where('message_id', $lastMessage->message_id)
            ->latest('id')
            ->first();

        return $otp && ! $otp->isExpired();
    }

    private function attemptOtpSend(string $phone): ?OneTimePassword
    {
        $senderId = Setting::where('key', 'default_sender')->first();

        if (! $senderId) {
            return null;
        }

        /** @var Sender|null $sender */
        $sender = Sender::find($senderId->value);

        if (! $sender) {
            return null;
        }

        return $this->sendOtp($sender, $phone);
    }

    private function sendOtp(Sender $sender, string $recipient): ?OneTimePassword
    {
        try {
            return DB::transaction(function () use ($sender, $recipient) {
                $code = str_pad((string) random_int(0, 999999), 6, '0', STR_PAD_LEFT);

                $body = 'Your OTP is {{code}}';

                $body = str_replace('{{code}}', $code, $body);

                $message = Message::create([
                    'short_message' => $body,
                    'sender_id' => $sender->id,
                    'message_type' => 'otp',
                    'send_type' => 'single',
                    'message_consumption' => 1,
                ]);

                $message->messages()->create([
                    'number' => $recipient,
                    'status' => 'pending',
                ]);

                $otp = OneTimePassword::create([
                    'expiration_period' => 5,
                    'length' => 6,
                    'code' => $code,
                    'message_id' => $message->id,
                ]);

                $jasminClient = app(JasminClient::class);
                MessageJobCreator::dispatch($message, $jasminClient);

                return $otp;
            });
        } catch (Throwable $e) {
            Log::error('OTP Send Failed: '.$e->getMessage());

            return null;
        }
    }
}
