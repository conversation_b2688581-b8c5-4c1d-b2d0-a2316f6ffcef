<?php

declare(strict_types=1);

use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\OneTimePassword;
use App\Models\RegistrationRequest;
use App\Models\Sender;
use App\Models\Setting;
use App\Models\User;
use Carbon\CarbonImmutable;

use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\postJson;
use function Pest\Laravel\seed;

beforeEach(function () {
    // Seed the database before each test
    seed();
});

it('registers a new registration request successfully', function () {

    // Arrange: Prepare valid request data
    $data = [
        'name' => 'John Doe',
        'phone' => '00218911253207',
        'email' => '<EMAIL>',
        'city' => 'New York',
        'company' => 'Example Inc.',
        'category' => 'IT',
        'have_smsapi' => 'yes',
        'target' => ['OTP', 'Campaign'],
        'system_type' => 'Custom',
        'notes' => 'Test note',
    ];

    // Act: Make a POST request to the endpoint
    $response = postJson('/api/registration-request', $data);

    // Assert: Check the response
    $response->assertCreated()
        ->assertJson([
            'message' => 'The request has been registered successfully.',
        ]);

    // Assert: Check the database
    assertDatabaseHas('registration_requests', [
        'email' => '<EMAIL>',
    ]);
});

// Test case: returns conflict if email already exists
it('returns conflict if email already exists', function () {
    // Arrange: Create a user and a registration request with the same email
    $email = '<EMAIL>'; // Changed email for cache busting
    // Ensure User model is also created if the intent is to check uniqueness across users table too
    // User::factory()->create(['email' => $email]);
    RegistrationRequest::factory()->create(['email' => $email, 'status' => 'pending']);

    $data = [
        'name' => 'Jane Smith', // Changed name for cache busting
        'phone' => '00218913651235', // Changed phone for cache busting
        'email' => $email,
    ];

    // Act: Make a POST request to the endpoint
    $response = postJson('/api/registration-request', $data);

    // Assert: Check the response
    $response->assertStatus(422) // Expect 422 due to validation errors
        ->assertJsonValidationErrorFor('email')
        ->assertJsonPath('errors.email.0', 'The email has already been taken.');
});

it('returns validation errors for invalid data', function () {
    // Arrange: Prepare invalid request data
    $data = [
        'name' => '',
        'phone' => '',
        'email' => 'invalid-email',
    ];

    // Act: Make a POST request to the endpoint
    $response = postJson('/api/registration-request', $data);

    // Assert: Check the response
    $response->assertStatus(422)
        ->assertJsonValidationErrors(['name', 'phone', 'email']);
});

it('verifies OTP successfully', function () {

    // Arrange: Create a registration request
    $registrationRequest = RegistrationRequest::factory()->create([
        'phone' => '00218911322556',
        'status' => 'pending',
    ]);

    // Create a sender for OTP
    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    // Create message, message receipt and OTP
    $message = Message::create([
        'short_message' => 'Your OTP is 123456',
        'sender_id' => $sender->id,
        'message_type' => 'otp',
        'send_type' => 'single',
        'message_consumption' => 1,
    ]);

    $messageReceipt = $message->messages()->create([
        'number' => '00218911322556',
        'status' => 'delivered',
    ]);

    $otp = OneTimePassword::create([
        'expiration_period' => 5,
        'length' => 6,
        'code' => '123456',
        'message_id' => $message->id,
    ]);

    // Act: Make a POST request to verify OTP
    $response = postJson('/api/registration-request/verify', [
        'request_id' => $otp->id,
        'code' => '123456',
    ]);

    // Assert: Check the response
    $response->assertOk()
        ->assertJson([
            'message' => 'OTP verified successfully',
        ]);

    // Assert: OTP should be marked as verified
    $verifiedOtp = OneTimePassword::find($otp->id);
    expect($verifiedOtp->verified_at)->not->toBeNull()
        ->and($verifiedOtp->verified_at)->toBeInstanceOf(CarbonImmutable::class);
});

it('returns error for invalid OTP code', function () {
    // Arrange: Create a registration request and OTP
    $registrationRequest = RegistrationRequest::factory()->create([
        'phone' => '1234567890',
        'status' => 'pending',
    ]);

    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    $message = Message::create([
        'short_message' => 'Your OTP is 123456',
        'sender_id' => $sender->id,
        'message_type' => 'otp',
        'send_type' => 'single',
        'message_consumption' => 1,
    ]);

    $messageReceipt = $message->messages()->create([
        'number' => '1234567890',
        'status' => 'delivered',
    ]);

    $otp = OneTimePassword::create([
        'expiration_period' => 5,
        'length' => 6,
        'code' => '123456',
        'message_id' => $message->id,
    ]);

    // Act: Make a POST request with wrong OTP code
    $response = postJson('/api/registration-request/verify', [
        'request_id' => $otp->id,
        'code' => '654321', // Wrong code
    ]);

    // Assert: Check the response
    $response->assertStatus(401)
        ->assertJson([
            'message' => 'Invalid OTP',
        ]);
});

it('returns error for expired OTP', function () {
    // Arrange: Create a registration request and expired OTP
    $registrationRequest = RegistrationRequest::factory()->create([
        'phone' => '1234567890',
        'status' => 'pending',
    ]);

    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    $message = Message::create([
        'short_message' => 'Your OTP is 123456',
        'sender_id' => $sender->id,
        'message_type' => 'otp',
        'send_type' => 'single',
        'message_consumption' => 1,
    ]);

    $messageReceipt = $message->messages()->create([
        'number' => '1234567890',
        'status' => 'delivered',
    ]);

    $otp = OneTimePassword::create([
        'expiration_period' => 5,
        'length' => 6,
        'code' => '123456',
        'message_id' => $message->id,
        'created_at' => now()->subMinutes(10), // Created 10 minutes ago (expired)
    ]);

    // Act: Make a POST request with expired OTP
    $response = postJson('/api/registration-request/verify', [
        'request_id' => $otp->id,
        'code' => '123456',
    ]);

    // Assert: Check the response
    $response->assertStatus(401)
        ->assertJson([
            'message' => 'OTP has expired',
        ]);
});

it('returns error for already verified OTP', function () {
    // Arrange: Create a registration request and already verified OTP
    $registrationRequest = RegistrationRequest::factory()->create([
        'phone' => '1234567890',
        'status' => 'pending',
    ]);

    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    $message = Message::create([
        'short_message' => 'Your OTP is 123456',
        'sender_id' => $sender->id,
        'message_type' => 'otp',
        'send_type' => 'single',
        'message_consumption' => 1,
    ]);

    $messageReceipt = $message->messages()->create([
        'number' => '1234567890',
        'status' => 'delivered',
    ]);

    $otp = OneTimePassword::create([
        'expiration_period' => 5,
        'length' => 6,
        'code' => '123456',
        'message_id' => $message->id,
        'verified_at' => now()->subMinutes(5), // Already verified
    ]);

    // Act: Make a POST request with already verified OTP
    $response = postJson('/api/registration-request/verify', [
        'request_id' => $otp->id,
        'code' => '123456',
    ]);

    // Assert: Check the response
    $response->assertStatus(400)
        ->assertJson([
            'message' => 'OTP already verified',
        ]);
});

it('resends OTP successfully', function () {
    // Arrange: Prepare valid request data
    $data = [
        'name' => 'John Doe',
        'phone' => '00218911253207',
        'email' => '<EMAIL>',
        'city' => 'New York',
        'company' => 'Example Inc.',
        'category' => 'IT',
        'have_smsapi' => 'yes',
        'target' => ['OTP', 'Campaign'],
        'system_type' => 'Custom',
        'notes' => 'Test note',
    ];

    // Act: Make a POST request to the endpoint
    postJson('/api/registration-request', $data); // Initial registration

    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    // Act: Make a POST request to resend OTP
    $response = postJson('/api/registration-request/resend', [
        'phone' => '00218911253207', // Use the correct phone number from the initial registration
    ]);

    // Assert: Check the response
    $response->assertOk()
        ->assertJson([
            'message' => 'OTP resent successfully',
        ])
        ->assertJsonStructure([
            'message',
            'request_id',
        ]);
});

it('returns error when trying to resend OTP with active OTP', function () {
    // Arrange: Create a registration request, and an active OTP associated with it.
    $phoneNumber = '00218911253207';
    RegistrationRequest::factory()->create([
        'phone' => $phoneNumber,
        'status' => 'pending',
    ]);

    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    // Create a message linked to the sender
    $message = Message::factory()->create([
        'sender_id' => $sender->id,
        'message_type' => 'otp',
    ]);

    // Create a message receipt for the phone number, linked to the message
    MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'number' => $phoneNumber,
    ]);

    // Create an active (non-expired) OTP linked to the message
    OneTimePassword::create([
        'message_id' => $message->id,
        'code' => '123456',
        'length' => 6,
        'expiration_period' => 5, // minutes
    ]);

    // Act: Make a POST request to resend OTP for the same phone number
    $response = postJson('/api/registration-request/resend', [
        'phone' => $phoneNumber,
    ]);

    // Assert: Check the response
    $response->assertStatus(400)
        ->assertJson([
            'message' => 'You have already sent an OTP to this number',
        ]);
});

it('verifies OTP not found', function () {

    // Arrange: Create a registration request
    $registrationRequest = RegistrationRequest::factory()->create([
        'phone' => '00218911322556',
        'status' => 'pending',
    ]);

    // Create a sender for OTP
    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    // Create message, message receipt and OTP
    $message = Message::create([
        'short_message' => 'Your OTP is 123456',
        'sender_id' => $sender->id,
        'message_type' => 'otp',
        'send_type' => 'single',
        'message_consumption' => 1,
    ]);

    $messageReceipt = $message->messages()->create([
        'number' => '00218911322556',
        'status' => 'delivered',
    ]);

    // Act: Make a POST request to verify OTP
    $response = postJson('/api/registration-request/verify', [
        'request_id' => str()->uuid(),
        'code' => '123456',
    ]);

    // Assert: Check the response
    $response->assertStatus(404)
        ->assertJson([
            'message' => 'OTP not found',
        ]);
});

it('resends Message not found', function () {
    // Arrange: Create a registration request
    $registrationRequest = RegistrationRequest::factory()->create([
        'phone' => '00218911322556',
        'status' => 'pending',
    ]);

    // Create a sender for OTP
    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    // Create message, message receipt and OTP
    $message = Message::create([
        'short_message' => 'Your OTP is 123456',
        'sender_id' => $sender->id,
        'message_type' => 'otp',
        'send_type' => 'single',
        'message_consumption' => 1,
    ]);

    $message->messages()->create([
        'number' => '00218911322556',
        'status' => 'delivered',
    ]);

    $otp = OneTimePassword::create([
        'expiration_period' => 5,
        'length' => 6,
        'code' => '123456',
        'message_id' => $message->id,
    ]);

    $message->messages()->delete();

    // Act: Make a POST request to verify OTP
    $response = postJson('/api/registration-request/verify', [
        'request_id' => $otp->id,
        'code' => '123456',
    ]);

    // Assert: Check the response
    $response->assertStatus(404)
        ->assertJson([
            'message' => 'Message not found',
        ]);
});

it('resends Registration request not found', function () {
    // Arrange: Create a registration request
    $registrationRequest = RegistrationRequest::factory()->create([
        'phone' => '00218911322556',
        'status' => 'pending',
    ]);

    // Create a sender for OTP
    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    // Create message, message receipt and OTP
    $message = Message::create([
        'short_message' => 'Your OTP is 123456',
        'sender_id' => $sender->id,
        'message_type' => 'otp',
        'send_type' => 'single',
        'message_consumption' => 1,
    ]);

    $message->messages()->create([
        'number' => '00218911322556',
        'status' => 'delivered',
    ]);

    $otp = OneTimePassword::create([
        'expiration_period' => 5,
        'length' => 6,
        'code' => '123456',
        'message_id' => $message->id,
    ]);

    $registrationRequest->delete();

    // Act: Make a POST request to verify OTP
    $response = postJson('/api/registration-request/verify', [
        'request_id' => $otp->id,
        'code' => '123456',
    ]);

    // Assert: Check the response
    $response->assertStatus(404)
        ->assertJson([
            'message' => 'Registration request not found',
        ]);
});

it('handles database transaction failure during OTP verification', function () {
    // Arrange: Create a registration request
    $registrationRequest = RegistrationRequest::factory()->create([
        'phone' => '00218911322556',
        'status' => 'pending',
    ]);

    // Create a sender for OTP
    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    // Create message, message receipt and OTP
    $message = Message::create([
        'short_message' => 'Your OTP is 123456',
        'sender_id' => $sender->id,
        'message_type' => 'otp',
        'send_type' => 'single',
        'message_consumption' => 1,
    ]);

    $messageReceipt = $message->messages()->create([
        'number' => '00218911322556',
        'status' => 'delivered',
    ]);

    $otp = OneTimePassword::create([
        'expiration_period' => 5,
        'length' => 6,
        'code' => '123456',
        'message_id' => $message->id,
    ]);

    // Mock DB to throw exception during transaction
    DB::shouldReceive('transaction')
        ->once()
        ->andThrow(new \Exception('Database error'));

    // Act: Make a POST request to verify OTP
    $response = postJson('/api/registration-request/verify', [
        'request_id' => $otp->id,
        'code' => '123456',
    ]);

    // Assert: Check the response
    $response->assertStatus(500)
        ->assertJson([
            'message' => 'Something went wrong',
        ]);

    // Assert: OTP should not be marked as verified
    $verifiedOtp = OneTimePassword::find($otp->id);
    expect($verifiedOtp->verified_at)->toBeNull();
});

it('returns error when registration request not found for phone number', function () {
    // Create a sender for OTP
    $sender = Sender::factory()->create();
    Setting::create(['key' => 'default_sender', 'value' => $sender->id]);

    // Create message, message receipt and OTP for a phone number with no registration request
    $message = Message::create([
        'short_message' => 'Your OTP is 123456',
        'sender_id' => $sender->id,
        'message_type' => 'otp',
        'send_type' => 'single',
        'message_consumption' => 1,
    ]);

    $messageReceipt = $message->messages()->create([
        'number' => '00218911322556', // No registration request with this number
        'status' => 'delivered',
    ]);

    $otp = OneTimePassword::create([
        'expiration_period' => 5,
        'length' => 6,
        'code' => '123456',
        'message_id' => $message->id,
    ]);

    // Act: Make a POST request to verify OTP
    $response = postJson('/api/registration-request/verify', [
        'request_id' => $otp->id,
        'code' => '123456',
    ]);

    // Assert: Check the response
    $response->assertStatus(404)
        ->assertJson([
            'message' => 'Registration request not found',
        ]);
});
